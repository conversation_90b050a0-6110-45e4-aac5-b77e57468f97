'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, Leaf } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { name: 'How It Works', href: '#how-it-works' },
    { name: 'Features', href: '#features' },
    { name: 'Impact', href: '#impact' },
    { name: 'Community', href: '#community' },
  ];

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all-smooth ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-lg' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <Leaf className={`h-8 w-8 transition-all-smooth ${
                isScrolled ? 'text-brand-pine-green' : 'text-white'
              } group-hover:scale-110`} />
              <div className="absolute inset-0 bg-brand-pine-green/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-300"></div>
            </div>
            <span className={`text-2xl font-bold transition-all-smooth ${
              isScrolled ? 'text-brand-rich-black' : 'text-white'
            }`}>
              Pedi
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`text-sm font-medium transition-all-smooth hover:scale-105 ${
                  isScrolled 
                    ? 'text-gray-700 hover:text-brand-pine-green' 
                    : 'text-white/90 hover:text-white'
                }`}
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Desktop CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link href="/auth">
              <Button 
                variant="ghost" 
                className={`transition-all-smooth ${
                  isScrolled 
                    ? 'text-gray-700 hover:text-brand-pine-green hover:bg-brand-pine-green/10' 
                    : 'text-white hover:text-white hover:bg-white/10'
                }`}
              >
                Sign In
              </Button>
            </Link>
            <Link href="/auth">
              <Button 
                className={`transition-all-smooth hover-lift ${
                  isScrolled 
                    ? 'bg-brand-pine-green hover:bg-brand-dark-green text-white' 
                    : 'bg-white text-brand-pine-green hover:bg-gray-100'
                }`}
              >
                Get Started
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className={`lg:hidden p-2 rounded-md transition-all-smooth ${
              isScrolled 
                ? 'text-gray-700 hover:bg-gray-100' 
                : 'text-white hover:bg-white/10'
            }`}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-md rounded-lg mt-2 shadow-lg animate-fade-in-up">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-gray-700 hover:text-brand-pine-green hover:bg-brand-pine-green/10 rounded-md transition-all-smooth"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <div className="pt-4 space-y-2">
                <Link href="/auth" className="block">
                  <Button 
                    variant="ghost" 
                    className="w-full text-gray-700 hover:text-brand-pine-green hover:bg-brand-pine-green/10"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth" className="block">
                  <Button 
                    className="w-full bg-brand-pine-green hover:bg-brand-dark-green text-white"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
