'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, Play, Sparkles, Recycle, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-rich-black via-brand-black to-brand-dark-green">
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-brand-pine-green/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-brand-pine-green/30 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-white/5 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        
        {/* Gradient mesh overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-pine-green/5 to-transparent animate-shimmer"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Badge */}
          <div className={`inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-8 transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Sparkles className="h-4 w-4 text-brand-pine-green" />
            <span className="text-white text-sm font-medium">Kenya's First Sustainable Fashion Platform</span>
          </div>

          {/* Main Headline */}
          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            The Future of Fashion is{' '}
            <span className="gradient-text bg-gradient-to-r from-brand-pine-green to-green-400 bg-clip-text text-transparent">
              Circular
            </span>
          </h1>

          {/* Subtitle */}
          <p className={`text-xl sm:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Join Kenya's sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion 
            while earning Pedi tokens and making a positive impact on our environment.
          </p>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12 transition-all duration-1000 delay-600 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Link href="/auth">
              <Button 
                size="lg" 
                className="bg-brand-pine-green hover:bg-brand-dark-green text-white px-8 py-4 text-lg font-semibold hover-lift group"
              >
                Start Your Journey
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            
            <Button 
              variant="ghost" 
              size="lg" 
              className="text-white hover:bg-white/10 px-8 py-4 text-lg font-semibold group"
            >
              <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className={`grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto transition-all duration-1000 delay-800 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">10K+</div>
              <div className="text-gray-400">Clothes Exchanged</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">5K+</div>
              <div className="text-gray-400">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">2.5T</div>
              <div className="text-gray-400">CO₂ Saved (kg)</div>
            </div>
          </div>
        </div>

        {/* Floating Action Icons */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-4">
            <div className="bg-white/10 backdrop-blur-sm rounded-full p-3 animate-float">
              <Recycle className="h-6 w-6 text-brand-pine-green" />
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-full p-3 animate-float" style={{ animationDelay: '1s' }}>
              <Heart className="h-6 w-6 text-red-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
