'use client';

import { useEffect, useRef, useState } from 'react';
import { 
  Recycle, 
  Coins, 
  Heart, 
  Users, 
  Leaf, 
  Shield, 
  Smartphone, 
  TrendingUp,
  ArrowRight 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const features = [
  {
    icon: Recycle,
    title: 'Circular Fashion Exchange',
    description: 'Swap your pre-loved clothes with others in the community. Give your wardrobe a refresh while reducing textile waste.',
    color: 'text-brand-pine-green',
    bgColor: 'bg-brand-pine-green/10',
  },
  {
    icon: Coins,
    title: 'Pedi Token Rewards',
    description: 'Earn tokens for every sustainable action. Use them to unlock premium features and exclusive exchanges.',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    icon: Heart,
    title: 'Charity Donations',
    description: 'Donate clothes directly to local charities and make a meaningful impact in your community.',
    color: 'text-red-500',
    bgColor: 'bg-red-50',
  },
  {
    icon: Users,
    title: 'Community Driven',
    description: 'Connect with like-minded fashion enthusiasts who share your passion for sustainability.',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    icon: Leaf,
    title: 'Environmental Impact',
    description: 'Track your carbon footprint reduction and see the positive environmental impact of your choices.',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    icon: Shield,
    title: 'Secure Transactions',
    description: 'Safe and secure exchanges with built-in verification and M-Pesa integration for seamless payments.',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
];

const benefits = [
  {
    icon: Smartphone,
    title: 'Mobile-First Design',
    description: 'Optimized for mobile use with an intuitive interface that makes sustainable fashion accessible to everyone.',
  },
  {
    icon: TrendingUp,
    title: 'AI-Powered Matching',
    description: 'Smart algorithms help you find the perfect pieces and suggest sustainable fashion choices.',
  },
];

export default function Features() {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleItems(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('[data-index]');
    elements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section id="features" ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16" data-index="0">
          <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-bold text-brand-rich-black mb-6 transition-all duration-1000 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Why Choose <span className="gradient-text">Pedi</span>?
          </h2>
          <p className={`text-xl text-gray-600 max-w-3xl mx-auto transition-all duration-1000 delay-200 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Discover the features that make Pedi the leading sustainable fashion platform in Kenya
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index}
                data-index={index + 1}
                className={`p-6 hover-lift transition-all duration-1000 delay-${(index + 1) * 100} ${
                  visibleItems.includes(index + 1) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                } hover:shadow-xl border-0 bg-white`}
              >
                <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                  <Icon className={`h-6 w-6 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-brand-rich-black mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            );
          })}
        </div>

        {/* Additional Benefits */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div data-index="7" className={`transition-all duration-1000 ${
            visibleItems.includes(7) ? 'animate-fade-in-left opacity-100' : 'opacity-0'
          }`}>
            <h3 className="text-2xl sm:text-3xl font-bold text-brand-rich-black mb-6">
              Built for the Modern Sustainable Consumer
            </h3>
            <div className="space-y-6">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-brand-pine-green/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="h-5 w-5 text-brand-pine-green" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-brand-rich-black mb-2">
                        {benefit.title}
                      </h4>
                      <p className="text-gray-600">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div data-index="8" className={`transition-all duration-1000 delay-300 ${
            visibleItems.includes(8) ? 'animate-fade-in-right opacity-100' : 'opacity-0'
          }`}>
            <div className="bg-gradient-to-br from-brand-pine-green to-brand-dark-green rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to Make a Difference?</h3>
              <p className="text-white/90 mb-6 leading-relaxed">
                Join thousands of Kenyans who are already making sustainable fashion choices. 
                Start your journey today and be part of the circular fashion revolution.
              </p>
              <Button 
                size="lg" 
                className="bg-white text-brand-pine-green hover:bg-gray-100 font-semibold group"
              >
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
