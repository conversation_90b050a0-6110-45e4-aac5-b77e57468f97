'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Users, Leaf, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

const stats = [
  { value: '10K+', label: 'Items Exchanged', icon: ArrowRight },
  { value: '5K+', label: 'Happy Users', icon: Users },
  { value: '2.5T', label: 'CO₂ Saved (kg)', icon: Leaf },
  { value: '50+', label: 'Charity Partners', icon: Heart },
];

export default function CTA() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-rich-black via-brand-black to-brand-dark-green">
        {/* Animated background elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-brand-pine-green/20 rounded-full animate-float"></div>
        <div className="absolute top-20 right-20 w-16 h-16 bg-white/10 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-20 w-12 h-12 bg-brand-pine-green/30 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 bg-white/5 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        
        {/* Gradient mesh overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-pine-green/10 to-transparent animate-shimmer"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className={`inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-8 transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Sparkles className="h-4 w-4 text-brand-pine-green" />
            <span className="text-white text-sm font-medium">Join the Sustainable Fashion Revolution</span>
          </div>

          {/* Main Headline */}
          <h2 className={`text-3xl sm:text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Ready to Transform Your{' '}
            <span className="gradient-text bg-gradient-to-r from-brand-pine-green to-green-400 bg-clip-text text-transparent">
              Wardrobe
            </span>?
          </h2>

          {/* Subtitle */}
          <p className={`text-xl sm:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Join thousands of Kenyans making sustainable fashion choices. Start your journey today and be part of the change.
          </p>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16 transition-all duration-1000 delay-600 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Link href="/auth">
              <Button 
                size="lg" 
                className="bg-brand-pine-green hover:bg-brand-dark-green text-white px-12 py-4 text-lg font-semibold hover-lift group"
              >
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            
            <Link href="/browse">
              <Button 
                variant="outline" 
                size="lg" 
                className="border-white text-white hover:bg-white hover:text-brand-pine-green px-12 py-4 text-lg font-semibold transition-all-smooth"
              >
                Explore Items
              </Button>
            </Link>
          </div>

          {/* Stats Grid */}
          <div className={`grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto transition-all duration-1000 delay-800 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div 
                  key={index} 
                  className="text-center group hover-lift"
                  style={{ animationDelay: `${(index + 1) * 200}ms` }}
                >
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 transition-all-smooth group-hover:bg-white/20">
                    <Icon className="h-8 w-8 text-brand-pine-green mx-auto mb-4 group-hover:scale-110 transition-transform" />
                    <div className="text-3xl font-bold text-white mb-2 group-hover:scale-105 transition-transform">
                      {stat.value}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {stat.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Trust indicators */}
          <div className={`mt-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <p className="text-gray-400 text-sm mb-6">Trusted by sustainable fashion enthusiasts across Kenya</p>
            <div className="flex items-center justify-center space-x-8 opacity-60">
              <div className="text-white text-sm font-medium">✓ Secure Transactions</div>
              <div className="text-white text-sm font-medium">✓ M-Pesa Integration</div>
              <div className="text-white text-sm font-medium">✓ Community Verified</div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom wave decoration */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none" 
          className="relative block w-full h-16 fill-gray-50"
        >
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
          opacity=".25"
        ></path>
          <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" 
          opacity=".5"
        ></path>
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
        ></path>
        </svg>
      </div>
    </section>
  );
}
