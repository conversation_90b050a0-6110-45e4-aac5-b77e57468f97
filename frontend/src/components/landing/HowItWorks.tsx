'use client';

import { useEffect, useRef, useState } from 'react';
import { Camera, Search, RefreshCw, Gift, ArrowRight } from 'lucide-react';

const steps = [
  {
    icon: Camera,
    title: 'List Your Items',
    description: 'Take photos of your pre-loved clothes and create detailed listings with descriptions, sizes, and condition.',
    details: ['High-quality photos', 'Detailed descriptions', 'Size and condition info', 'Set your preferences'],
  },
  {
    icon: Search,
    title: 'Discover & Browse',
    description: 'Explore thousands of unique pieces from other users. Use filters to find exactly what you\'re looking for.',
    details: ['Smart search filters', 'Category browsing', 'Wishlist creation', 'AI recommendations'],
  },
  {
    icon: RefreshCw,
    title: 'Exchange & Swap',
    description: 'Connect with other users to arrange swaps using Pedi tokens or direct exchanges. Safe and secure transactions.',
    details: ['Token-based exchanges', 'Direct swaps', 'Secure messaging', 'M-Pesa integration'],
  },
  {
    icon: Gift,
    title: 'Earn & Give Back',
    description: 'Earn Pedi tokens for every sustainable action and donate clothes to local charities to make a positive impact.',
    details: ['Earn tokens for actions', 'Charity donations', 'Community impact', 'Environmental tracking'],
  },
];

export default function HowItWorks() {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleItems(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('[data-index]');
    elements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section id="how-it-works" ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16" data-index="0">
          <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-bold text-brand-rich-black mb-6 transition-all duration-1000 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            How <span className="gradient-text">Pedi</span> Works
          </h2>
          <p className={`text-xl text-gray-600 max-w-3xl mx-auto transition-all duration-1000 delay-200 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Simple steps to start your sustainable fashion journey
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left side - Steps */}
          <div className="space-y-8">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = activeStep === index;
              
              return (
                <div 
                  key={index}
                  data-index={index + 1}
                  className={`flex items-start space-x-6 p-6 rounded-2xl transition-all duration-1000 delay-${(index + 1) * 200} cursor-pointer ${
                    visibleItems.includes(index + 1) ? 'animate-fade-in-left opacity-100' : 'opacity-0'
                  } ${isActive ? 'bg-brand-pine-green/5 border-2 border-brand-pine-green/20' : 'hover:bg-gray-50'}`}
                  onClick={() => setActiveStep(index)}
                >
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-300 ${
                    isActive 
                      ? 'bg-brand-pine-green text-white scale-110' 
                      : 'bg-gray-100 text-gray-600 hover:bg-brand-pine-green/10 hover:text-brand-pine-green'
                  }`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`text-sm font-bold px-3 py-1 rounded-full transition-all duration-300 ${
                        isActive 
                          ? 'bg-brand-pine-green text-white' 
                          : 'bg-gray-200 text-gray-600'
                      }`}>
                        Step {index + 1}
                      </span>
                      <h3 className={`text-xl font-bold transition-all duration-300 ${
                        isActive ? 'text-brand-pine-green' : 'text-brand-rich-black'
                      }`}>
                        {step.title}
                      </h3>
                    </div>
                    
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {step.description}
                    </p>
                    
                    {isActive && (
                      <ul className="space-y-2 animate-fade-in-up">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center space-x-2 text-sm text-gray-500">
                            <ArrowRight className="h-4 w-4 text-brand-pine-green" />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Right side - Visual */}
          <div data-index="5" className={`transition-all duration-1000 delay-600 ${
            visibleItems.includes(5) ? 'animate-fade-in-right opacity-100' : 'opacity-0'
          }`}>
            <div className="relative">
              {/* Main visual container */}
              <div className="bg-gradient-to-br from-brand-pine-green to-brand-dark-green rounded-3xl p-8 text-white">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-4">
                    {steps[activeStep].title}
                  </h3>
                  <p className="text-white/90">
                    {steps[activeStep].description}
                  </p>
                </div>

                {/* Step indicator */}
                <div className="flex justify-center space-x-2 mb-6">
                  {steps.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveStep(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        activeStep === index 
                          ? 'bg-white scale-125' 
                          : 'bg-white/30 hover:bg-white/50'
                      }`}
                    />
                  ))}
                </div>

                {/* Animated icon */}
                <div className="text-center">
                  <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto animate-scale-in">
                    {React.createElement(steps[activeStep].icon, { 
                      className: "h-12 w-12 text-white" 
                    })}
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-float"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-400 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>
        </div>

        {/* Progress bar */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className="bg-gradient-to-r from-brand-pine-green to-brand-dark-green h-2 rounded-full transition-all duration-1000"
              style={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
          <p className="text-center text-gray-500 text-sm">
            Step {activeStep + 1} of {steps.length}
          </p>
        </div>
      </div>
    </section>
  );
}
